"""
使用Waitress WSGI服务器运行Flask应用（生产环境推荐）
"""
import os
from waitress import serve
from app import create_app
from config import config
from werkzeug.middleware.proxy_fix import ProxyFix

def main():
    # 根据环境变量选择配置
    config_name = os.environ.get('FLASK_ENV', 'production')
    app = create_app(config[config_name])
    
    # 配置代理支持，处理反向代理的头信息
    app.wsgi_app = ProxyFix(
        app.wsgi_app,
        x_for=1,  # 信任1个代理的X-Forwarded-For头
        x_proto=1,  # 信任1个代理的X-Forwarded-Proto头
        x_host=1,  # 信任1个代理的X-Forwarded-Host头
        x_prefix=1  # 信任1个代理的X-Forwarded-Prefix头
    )
    
    # 获取配置
    host = '127.0.0.1'
    port = 8080
    threads = 4  # 线程数
    
    print(f"启动模式: 生产模式 (Waitress WSGI服务器)")
    print(f"监听地址: {host}:{port}")
    print(f"线程数: {threads}")
    print("按 Ctrl+C 停止服务器")
    
    # 使用Waitress服务器
    serve(
        app,
        host=host,
        port=port,
        threads=threads,
        connection_limit=1000,
        cleanup_interval=30,
        channel_timeout=120
    )

if __name__ == '__main__':
    main()
