Write-Host "正在以生产模式启动系统..." -ForegroundColor Green

# 设置环境变量为生产模式
$env:FLASK_ENV = "production"

# 检查虚拟环境是否存在
if (-not (Test-Path "venv\Scripts\Activate.ps1")) {
    Write-Host "错误：虚拟环境不存在！请先运行 start.ps1 创建虚拟环境" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 激活虚拟环境
& "venv\Scripts\Activate.ps1"

# 检查依赖是否安装
if (-not (Test-Path "requirements.txt")) {
    Write-Host "错误：requirements.txt 文件不存在！" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 安装依赖
Write-Host "正在检查并安装依赖..." -ForegroundColor Yellow
pip install -r requirements.txt

# 启动应用（生产模式）
Write-Host "正在以生产模式启动应用..." -ForegroundColor Green
try {
    python run.py
}
catch {
    Write-Host "应用异常退出！错误信息：$($_.Exception.Message)" -ForegroundColor Red
    Read-Host "按任意键退出"
}

# 退出虚拟环境
deactivate
