import os
from app import create_app
from config import config
from werkzeug.middleware.proxy_fix import ProxyFix

# 根据环境变量选择配置
config_name = os.environ.get('FLASK_ENV', 'development')
app = create_app(config[config_name])

# 配置代理支持，处理反向代理的头信息
app.wsgi_app = ProxyFix(
    app.wsgi_app,
    x_for=1,  # 信任1个代理的X-Forwarded-For头
    x_proto=1,  # 信任1个代理的X-Forwarded-Proto头
    x_host=1,  # 信任1个代理的X-Forwarded-Host头
    x_prefix=1  # 信任1个代理的X-Forwarded-Prefix头
)

if __name__ == '__main__':
    # 绑定到8080端口，通过IIS代理对外提供服务
    # 根据配置自动选择debug模式
    debug_mode = app.config.get('DEBUG', False)
    print(f"启动模式: {'开发模式' if debug_mode else '生产模式'}")
    app.run(debug=debug_mode, host='127.0.0.1', port=8080, use_reloader=False, threaded=True)
