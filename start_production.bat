@echo off
echo 正在以生产模式启动系统...

:: 设置环境变量为生产模式
set FLASK_ENV=production

:: 检查虚拟环境是否存在
if not exist "venv\Scripts\activate.bat" (
    echo 错误：虚拟环境不存在！请先运行 start.bat 创建虚拟环境
    pause
    exit /b 1
)

:: 激活虚拟环境
call venv\Scripts\activate.bat

:: 检查依赖是否安装
if not exist "requirements.txt" (
    echo 错误：requirements.txt 文件不存在！
    pause
    exit /b 1
)

:: 安装依赖
echo 正在检查并安装依赖...
pip install -r requirements.txt

:: 启动应用（生产模式）
echo 正在以生产模式启动应用...
python run.py

:: 如果应用异常退出，暂停以便查看错误信息
if errorlevel 1 (
    echo 应用异常退出！
    pause
)

:: 退出虚拟环境
deactivate
